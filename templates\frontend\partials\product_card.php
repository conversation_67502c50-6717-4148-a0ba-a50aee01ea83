<?php

if (!isset($product) || !is_array($product)) {
    return;
}

$product_name = $product['name_pt'] ?? ($product['name'] ?? 'Produto');
$product_id = $product['id'] ?? 0;
$product_slug = $product['slug'] ?? '';
$product_price = $product['base_price'] ?? ($product['price'] ?? 0);
$display_image_url = $product['display_image_url'] ?? ($display_image_url ?? null);
$currency_symbol = get_setting('currency_symbol', '€');

$product_url = get_product_url($product_slug);

$is_digital = isset($product['product_type']) && $product['product_type'] === 'digital';

if (!isset($product['product_type'])) {
    $product_data = db_query(
        "SELECT product_type FROM products WHERE id = :id",
        [':id' => $product_id],
        true
    );
    $is_digital = $product_data && $product_data['product_type'] === 'digital';
}

$has_variations = false;
$variations_count = db_query(
    "SELECT COUNT(*) as count FROM product_variations WHERE product_id = :pid",
    [':pid' => $product_id],
    true
);
$has_variations = $variations_count && (int)$variations_count['count'] > 0;

$has_custom_fields = false;
$custom_fields_count = db_query(
    "SELECT COUNT(*) as count FROM product_custom_fields WHERE product_id = :pid",
    [':pid' => $product_id],
    true
);
$has_custom_fields = $custom_fields_count && (int)$custom_fields_count['count'] > 0;

$show_quick_add = ($is_digital || (!$has_variations && !$has_custom_fields));

$card_min_total_variation_modifier = 0;
$card_attribute_value_modifiers = [];

if ($has_variations) {

    $card_variations_raw = db_query(
        "SELECT pv.id as variation_id, pv.price_modifier_override, vv.value_id, av.price_modifier as value_price_modifier
         FROM product_variations pv
         JOIN variation_values vv ON pv.id = vv.variation_id
         JOIN attribute_values av ON vv.value_id = av.id
         WHERE pv.product_id = :product_id AND pv.is_active = 1",
        [':product_id' => $product_id]
    );

    $card_variation_details_temp = [];
    if ($card_variations_raw) {
        foreach ($card_variations_raw as $row) {
            if (!isset($card_variation_details_temp[$row['variation_id']])) {
                $card_variation_details_temp[$row['variation_id']] = [
                    'price_modifier_override' => $row['price_modifier_override'],
                    'attribute_modifiers_sum' => 0
                ];
            }

            if ($row['price_modifier_override'] === null) {
                 $card_variation_details_temp[$row['variation_id']]['attribute_modifiers_sum'] += (float)$row['value_price_modifier'];
            }
        }
    }

    if (!empty($card_variation_details_temp)) {
        $first_card_var_processed = false;
        $current_card_min_for_vars = PHP_FLOAT_MAX;
        foreach ($card_variation_details_temp as $var_detail) {
            $current_var_mod = ($var_detail['price_modifier_override'] !== null)
                ? (float)$var_detail['price_modifier_override']
                : (float)$var_detail['attribute_modifiers_sum'];

            if (!$first_card_var_processed || $current_var_mod < $current_card_min_for_vars) {
                $current_card_min_for_vars = $current_var_mod;
                $first_card_var_processed = true;
            }
        }
        if ($first_card_var_processed) {
            $card_min_total_variation_modifier = $current_card_min_for_vars;
        }
    }
}

$card_min_total_custom_field_modifier = 0;
if ($has_custom_fields) {

    require_once __DIR__ . '/../../../includes/custom_field_functions.php';
    $card_product_custom_fields = get_product_custom_fields($product_id);

    if (!empty($card_product_custom_fields)) {
        foreach ($card_product_custom_fields as $field) {
            $field_price_modifier_val = $field['price_modifier_override'] !== null ? $field['price_modifier_override'] : $field['price_modifier'];
            $field_modifier_float = (float)$field_price_modifier_val;

            if ($field['is_required']) {
                $card_min_total_custom_field_modifier += $field_modifier_float;
            } else {
                if ($field_modifier_float < 0) {
                    $card_min_total_custom_field_modifier += $field_modifier_float;
                }
            }
        }
    }
}

$card_initial_display_price = (float)$product_price + $card_min_total_variation_modifier + $card_min_total_custom_field_modifier;
$price_formatted = format_price($card_initial_display_price, $currency_symbol);

?>

<div class="product-card bg-gray-900 rounded-lg overflow-hidden group flex flex-col h-full shadow-md hover:shadow-lg transition-shadow duration-300">
    <div class="relative overflow-hidden aspect-square">
        <a href="<?= $product_url ?>" class="block w-full h-full">
            <?php if ($display_image_url): ?>
                <img src="<?= sanitize_input($display_image_url) ?>" alt="<?= sanitize_input($product_name) ?>" class="w-full h-full object-cover product-image transition-transform duration-300 group-hover:scale-105">
            <?php else: ?>
                <!-- Placeholder for missing image -->
                <div class="w-full h-full bg-gray-800 flex items-center justify-center">
                    <i class="ri-image-line text-3xl sm:text-4xl text-gray-500"></i>
                </div>
            <?php endif; ?>
        </a>

        <?php if ($is_digital): ?>
            <!-- Digital Product Badge -->
            <div class="absolute top-2 left-2 sm:top-3 sm:left-3 bg-primary bg-opacity-90 text-white text-xs font-medium px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-full flex items-center shadow-md backdrop-blur-sm transform transition-transform duration-300 hover:scale-105">
                <i class="ri-download-line mr-0.5 sm:mr-1"></i>
                <span class="text-[10px] sm:text-xs">Produto Digital</span>
            </div>
        <?php endif; ?>

        <?php if ($is_digital && $product_price == 0): ?>
            <!-- Free Digital Product Badge -->
            <div class="absolute bottom-2 left-2 sm:bottom-3 sm:left-3 bg-green-600 bg-opacity-95 text-white text-xs font-bold px-2 py-1 rounded shadow-md backdrop-blur-sm">
                <span class="text-[10px] sm:text-xs">GRÁTIS</span>
            </div>
        <?php endif; ?>
    </div>
    <div class="p-2 sm:p-3 md:p-4 flex flex-col flex-grow">
        <h3 class="text-sm sm:text-base md:text-lg font-medium mb-1 truncate flex-grow">
            <a href="<?= $product_url ?>" class="hover:text-primary transition" title="<?= sanitize_input($product_name) ?>">
                <?= sanitize_input($product_name) ?>
            </a>
        </h3>
        <div class="flex items-center justify-between mt-1 sm:mt-2">
            <span class="text-sm sm:text-base md:text-lg font-semibold">
                <?php if ($has_variations || $has_custom_fields): ?>
                    Desde:
                <?php endif; ?>
                <?= $price_formatted ?>
            </span>
            <?php if ($show_quick_add): ?>
                <button class="add-to-cart-btn p-1.5 sm:p-2 bg-gray-800 rounded-full hover:bg-primary transition text-white"
                        data-product-id="<?= $product_id ?>"
                        data-product-type="<?= $product_type ?? 'regular' ?>"
                        aria-label="Adicionar <?= sanitize_input($product_name) ?> ao carrinho">
                    <div class="w-4 h-4 sm:w-5 sm:h-5 flex items-center justify-center">
                        <i class="ri-shopping-cart-line"></i>
                    </div>
                </button>
            <?php else: ?>
                <a href="<?= $product_url ?>" class="p-1.5 sm:p-2 bg-gray-800 rounded-full hover:bg-primary transition text-white"
                   aria-label="Ver detalhes de <?= sanitize_input($product_name) ?>">
                    <div class="w-4 h-4 sm:w-5 sm:h-5 flex items-center justify-center">
                        <i class="ri-eye-line"></i>
                    </div>
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>
