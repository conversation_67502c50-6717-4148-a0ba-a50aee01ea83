<?php

function migrate_create_license_verification_tokens(PDO $pdo): bool
{
    $migration_name = 'create_license_verification_tokens';
    error_log("Running migration: {$migration_name}");

    try {
        // Check if this migration has already run
        $check_sql = "SELECT 1 FROM migrations WHERE name = :name";
        $stmt_check = $pdo->prepare($check_sql);
        $stmt_check->execute([':name' => $migration_name]);
        if ($stmt_check->fetch()) {
            error_log("Migration {$migration_name} has already been applied.");
            return true; // Migration already applied
        }

        $pdo->beginTransaction();

        // Check if table already exists
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='license_verification_tokens';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {
            // Create the license_verification_tokens table
            $pdo->exec("CREATE TABLE license_verification_tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_id INTEGER NOT NULL,
                verification_code TEXT NOT NULL,
                session_id TEXT,
                is_verified INTEGER NOT NULL DEFAULT 0,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                expires_at TEXT NOT NULL,
                FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE
            );");

            // Create indexes for performance
            $pdo->exec("CREATE INDEX idx_license_verification_tokens_license_id ON license_verification_tokens (license_id);");
            $pdo->exec("CREATE INDEX idx_license_verification_tokens_verification_code ON license_verification_tokens (verification_code);");
            $pdo->exec("CREATE INDEX idx_license_verification_tokens_session_id ON license_verification_tokens (session_id);");
            $pdo->exec("CREATE INDEX idx_license_verification_tokens_expires_at ON license_verification_tokens (expires_at);");

            error_log("Created license_verification_tokens table with indexes");
        } else {
            error_log("license_verification_tokens table already exists, skipping creation");
        }

        // Log the migration as completed
        $log_sql = "INSERT INTO migrations (name, executed_at) VALUES (:name, datetime('now', 'localtime'))";
        $stmt_log = $pdo->prepare($log_sql);
        $stmt_log->execute([':name' => $migration_name]);

        $pdo->commit();
        error_log("Migration {$migration_name} completed successfully");
        return true;

    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Migration {$migration_name} failed: " . $e->getMessage());
        return false;
    }
}

// If run directly, execute the migration
if (basename(__FILE__) == basename($_SERVER["SCRIPT_FILENAME"])) {
    require_once __DIR__ . '/../db.php'; // Adjust path as necessary to get $pdo
    $pdo = get_db_connection();
    if ($pdo) {
        // Ensure migrations table exists
        $pdo->exec("CREATE TABLE IF NOT EXISTS migrations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            executed_at TEXT NOT NULL
        )");
        
        if (migrate_create_license_verification_tokens($pdo)) {
            echo "Migration create_license_verification_tokens executed successfully.\n";
        } else {
            echo "Migration create_license_verification_tokens failed.\n";
        }
    } else {
        echo "Failed to connect to the database.\n";
    }
}
?>
