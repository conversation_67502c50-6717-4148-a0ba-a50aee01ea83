<?php

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/functions.php';

function calculate_variation_price(int $product_id, int $variation_id): float|false
{
    if ($product_id <= 0 || $variation_id <= 0) {
        return false;
    }


    $product_sql = "SELECT base_price FROM products WHERE id = :product_id AND is_active = 1";
    $product = db_query($product_sql, [':product_id' => $product_id], true);

    if (!$product) {
        return false;
    }

    $base_price = (float)$product['base_price'];


    $variation_sql = "SELECT price_modifier_override FROM product_variations WHERE id = :variation_id";
    $variation = db_query($variation_sql, [':variation_id' => $variation_id], true);

    if ($variation === false) {
        return false;
    }


    if ($variation['price_modifier_override'] !== null) {
        $price_modifier = (float)$variation['price_modifier_override'];
        return $base_price + $price_modifier;
    }


    $modifiers_sql = "SELECT SUM(av.price_modifier) as total_modifier
                     FROM variation_values vv
                     JOIN attribute_values av ON vv.value_id = av.id
                     WHERE vv.variation_id = :variation_id";

    $modifiers = db_query($modifiers_sql, [':variation_id' => $variation_id], true);

    if ($modifiers === false) {
        return false;
    }

    $total_modifier = (float)($modifiers['total_modifier'] ?? 0);


    return $base_price + $total_modifier;
}

function get_variation_attribute_string(int $variation_id): string
{
    if ($variation_id <= 0) {
        return '';
    }

    $sql = "SELECT a.name_pt as attribute_name, av.value_pt as value_name
            FROM variation_values vv
            JOIN attribute_values av ON vv.value_id = av.id
            JOIN attributes a ON av.attribute_id = a.id
            WHERE vv.variation_id = :variation_id
            ORDER BY a.name_pt, av.value_pt";

    $attributes = db_query($sql, [':variation_id' => $variation_id], false, true);

    if (!$attributes) {
        return '';
    }

    $parts = [];
    foreach ($attributes as $attr) {
        $parts[] = "{$attr['attribute_name']}: {$attr['value_name']}";
    }

    return implode(', ', $parts);
}

function check_variation_stock(int $variation_id, int $quantity): bool
{
    if ($variation_id <= 0 || $quantity <= 0) {
        return false;
    }

    $sql = "SELECT stock FROM product_variations WHERE id = :variation_id AND is_active = 1";
    $variation = db_query($sql, [':variation_id' => $variation_id], true);

    if (!$variation) {
        return false;
    }

    return (int)$variation['stock'] >= $quantity;
}

function get_product_by_id(int $id): array|false
{
    if ($id <= 0) return false;
    $sql = "SELECT * FROM products WHERE id = :id";
    $product = db_query($sql, [':id' => $id], true);
    return is_array($product) ? $product : false;
}

function get_product_by_slug(string $slug): array|false
{
    if (empty($slug)) return false;
    $sql = "SELECT * FROM products WHERE slug = :slug AND is_active = 1";
    $product = db_query($sql, [':slug' => $slug], true);
    return is_array($product) ? $product : false;
}

function get_product_images(int $product_id): array
{
    if ($product_id <= 0) return [];
    $sql = "SELECT * FROM product_images WHERE product_id = :pid ORDER BY sort_order ASC, id ASC";
    $images = db_query($sql, [':pid' => $product_id], false, true);
    return is_array($images) ? $images : [];
}

function get_product_default_image(int $product_id): array|false
{
    if ($product_id <= 0) return false;


    $sql = "SELECT * FROM product_images
            WHERE product_id = :pid AND is_default = 1
            LIMIT 1";

    $image = db_query($sql, [':pid' => $product_id], true);


    if (!$image) {
        $sql = "SELECT * FROM product_images
                WHERE product_id = :pid
                ORDER BY sort_order ASC, id ASC
                LIMIT 1";

        $image = db_query($sql, [':pid' => $product_id], true);
    }

    return is_array($image) ? $image : false;
}

function get_product_variations(int $product_id): array
{
    if ($product_id <= 0) return [];
    $sql = "SELECT * FROM product_variations WHERE product_id = :pid ORDER BY id ASC";
    $variations = db_query($sql, [':pid' => $product_id], false, true);
    return is_array($variations) ? $variations : [];
}

function get_all_attributes(): array
{
    $sql = "SELECT * FROM attributes ORDER BY name_pt ASC";
    $attributes = db_query($sql, [], false, true);
    return is_array($attributes) ? $attributes : [];
}

function save_product_custom_fields(int $product_id, array $custom_fields): bool
{
    if ($product_id <= 0) {
        return false;
    }


    if (empty($custom_fields)) {
        $custom_fields = [];
    }

    try {
        $pdo = get_db_connection();


        $pdo->beginTransaction();




        $delete_sql = "DELETE FROM product_custom_fields WHERE product_id = :pid";
        $delete_stmt = $pdo->prepare($delete_sql);
        $delete_stmt->execute([':pid' => $product_id]);


        if (!empty($custom_fields)) {

            $insert_sql = "INSERT INTO product_custom_fields (product_id, custom_field_id, price_modifier_override, sort_order, created_at)
                           VALUES (:pid, :field_id, :price_override, :sort_order, datetime('now', 'localtime'))";
            $insert_stmt = $pdo->prepare($insert_sql);

            $sort_order = 0;
            $fields_added = 0;

            foreach ($custom_fields as $field_id => $field_data) {

                if (isset($field_data['use']) && $field_data['use'] == 1) {
                    $price_override = !empty($field_data['price_modifier_override']) ? (float)$field_data['price_modifier_override'] : null;

                    $insert_stmt->execute([
                        ':pid' => $product_id,
                        ':field_id' => $field_id,
                        ':price_override' => $price_override,
                        ':sort_order' => $sort_order++
                    ]);

                    $fields_added++;
                } else {
                }
            }
        }


        $pdo->commit();
        return true;
    } catch (PDOException $e) {

        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function get_product_categories(int $product_id): array
{
    if ($product_id <= 0) return [];

    $sql = "SELECT c.id, c.name, c.slug
            FROM categories c
            JOIN product_categories pc ON c.id = pc.category_id
            WHERE pc.product_id = :pid
            ORDER BY c.name ASC";

    $categories = db_query($sql, [':pid' => $product_id], false, true);
    return is_array($categories) ? $categories : [];
}

function get_similar_products(int $current_product_id, string $current_description, int $limit = 6): array
{
    if ($current_product_id <= 0 || empty(trim($current_description))) {
        return [];
    }


    $current_description = mb_strtolower(strip_tags($current_description), 'UTF-8');
    $keywords = preg_split('/[\s,\.\-\(\)]+/', $current_description);
    $common_words = ['a', 'o', 'as', 'os', 'um', 'uma', 'de', 'do', 'da', 'dos', 'das', 'em', 'no', 'na', 'nos', 'nas', 'com', 'sem', 'para', 'por', 'e', 'ou', 'que', 'se', 'este', 'esta', 'isto', 'esse', 'essa', 'isso', 'aquele', 'aquela', 'aquilo', 'seu', 'sua', 'seus', 'suas', 'the', 'a', 'an', 'is', 'of', 'in', 'on', 'with', 'for', 'and', 'or', 'to', 'it', ''];
    $significant_keywords = array_filter($keywords, function($word) use ($common_words) {
        return !empty($word) && mb_strlen($word, 'UTF-8') > 3 && !in_array($word, $common_words);
    });
    $significant_keywords = array_unique($significant_keywords);

    if (empty($significant_keywords)) {
        return [];
    }


    $like_clauses = [];
    $params = [':current_pid' => $current_product_id];
    $i = 0;
    foreach ($significant_keywords as $keyword) {
        $param_name = ':keyword' . $i++;
        $like_clauses[] = "LOWER(p.description_pt) LIKE " . $param_name;
        $params[$param_name] = '%' . $keyword . '%';
    }
    $where_clause = implode(' OR ', $like_clauses);



    $sql = "SELECT
                p.id, p.name_pt, p.slug,
                pi.filename as image_filename
            FROM products p
            LEFT JOIN (
                SELECT product_id, filename, ROW_NUMBER() OVER(PARTITION BY product_id ORDER BY sort_order ASC, id ASC) as rn
                FROM product_images
            ) pi ON p.id = pi.product_id AND pi.rn = 1
            WHERE p.is_active = 1
              AND p.id != :current_pid
              AND ({$where_clause})";


    $potential_matches = db_query($sql, $params, false, true);

    if (!$potential_matches || empty($potential_matches)) {
        return [];
    }


    shuffle($potential_matches);
    $similar_products = array_slice($potential_matches, 0, $limit);


    foreach ($similar_products as &$prod) {
        $prod['image_url'] = $prod['image_filename'] ? get_product_image_url($prod['image_filename']) : get_asset_url('images/placeholder.png');
        $prod['url'] = get_product_url($prod['slug']);
    }
    unset($prod);

    return $similar_products;
}

function generate_random_sku(string $title, bool $is_simple = true, ?string $base_sku = null): string
{

    if (!$is_simple && !empty($base_sku)) {

        $base = substr(strtoupper($base_sku), 0, 6);

        while (strlen($base) < 6) {
            $base .= strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 1));
        }


        $unique_part = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 4));

        return $base . $unique_part;
    }


    $words = explode(' ', $title);
    $initials = '';
    foreach ($words as $word) {
        if (!empty($word)) {
            $initials .= strtoupper(mb_substr($word, 0, 1));
        }
    }


    $length = 10; // Changed: All SKUs are now 10 characters


    $sku = substr($initials, 0, $length);


    while (strlen($sku) < $length) {
        $sku .= strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 1));
    }

    return $sku;
}

function sku_exists(string $sku, ?int $exclude_product_id = null, bool $check_variations = true): bool
{
    if (empty($sku)) return false;


    $sql = "SELECT id FROM products WHERE sku = :sku";
    $params = [':sku' => $sku];

    if ($exclude_product_id) {
        $sql .= " AND id != :id";
        $params[':id'] = $exclude_product_id;
    }

    $existing_product = db_query($sql, $params, true);
    if ($existing_product) return true;


    if ($check_variations) {
        $var_sql = "SELECT id FROM product_variations WHERE sku = :sku";
        $var_params = [':sku' => $sku];

        $existing_variation = db_query($var_sql, $var_params, true);
        if ($existing_variation) return true;
    }

    return false;
}

function generate_unique_sku(string $title, bool $is_simple = true, ?string $base_sku = null, ?int $exclude_product_id = null): string
{

    $sku = generate_random_sku($title, $is_simple, $base_sku);


    $counter = 0;
    $original_sku = $sku;


    while (sku_exists($sku, $exclude_product_id) && $counter < 10) {
        $counter++;

        // All SKUs are now 10 characters, so use consistent logic
        $sku = substr($original_sku, 0, 9) . $counter;
    }

    return $sku;
}

function delete_product(int $product_id): bool
{
    if ($product_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {

        $pdo->beginTransaction();


        $images = get_product_images($product_id);


        $videos_sql = "SELECT * FROM product_videos WHERE product_id = :pid";
        $videos = db_query($videos_sql, [':pid' => $product_id], false, true);


        $product_data = db_query("SELECT product_type FROM products WHERE id = :id", [':id' => $product_id], true);
        $is_digital_product = $product_data && $product_data['product_type'] === 'digital';

        $digital_product = null;
        if ($is_digital_product) {
            require_once __DIR__ . '/digital_product_functions.php';
            $digital_product = get_digital_product_by_product_id($product_id);
        }


        $delete_sql = "DELETE FROM products WHERE id = :id";
        $stmt = $pdo->prepare($delete_sql);
        $stmt->execute([':id' => $product_id]);


        if ($stmt->rowCount() === 0) {

            $pdo->rollBack();
            return false;
        }


        $pdo->commit();


        if (!empty($images)) {
            $image_dir = PROJECT_ROOT . '/public/assets/images/products/';
            foreach ($images as $image) {
                $image_path = $image_dir . $image['filename'];
                if (file_exists($image_path)) {
                    if (@unlink($image_path)) {
                    } else {
                    }
                }
            }
        }


        if (!empty($videos)) {
            require_once __DIR__ . '/product_video_functions.php';
            foreach ($videos as $video) {
                if ($video['video_type'] === 'uploaded' && !empty($video['filename'])) {
                    $video_path = PROJECT_ROOT . '/public/assets/videos/products/' . $video['filename'];
                    if (file_exists($video_path)) {
                        @unlink($video_path);
                    }
                }

                if (!empty($video['thumbnail_filename'])) {
                    $thumbnail_path = PROJECT_ROOT . '/public/assets/images/video_thumbnails/' . $video['thumbnail_filename'];
                    if (file_exists($thumbnail_path)) {
                        @unlink($thumbnail_path);
                    }
                }
            }
        }


        if ($is_digital_product && $digital_product && !empty($digital_product['file_path'])) {
            $file_path = $digital_product['file_path'];
            if (file_exists($file_path)) {
                if (@unlink($file_path)) {
                } else {
                }
            }
        }

        return true;
    } catch (Exception $e) {

        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function get_min_max_product_prices(): array
{
    $sql = "SELECT MIN(base_price) as min_price, MAX(base_price) as max_price FROM products WHERE is_active = 1";
    $result = db_query($sql, [], true);

    if ($result && $result['min_price'] !== null && $result['max_price'] !== null) {
        return [
            'min_price' => (float)$result['min_price'],
            'max_price' => (float)$result['max_price']
        ];
    }
    return ['min_price' => 0.0, 'max_price' => 1000.0];
}

function get_all_product_seo_keywords(): array
{
    $sql = "SELECT seo_keywords FROM products WHERE is_active = 1 AND seo_keywords IS NOT NULL AND seo_keywords != ''";
    $results = db_query($sql, [], false, true);

    $all_keywords = [];
    if (is_array($results)) {
        foreach ($results as $row) {
            $keywords_string = $row['seo_keywords'];
            $keywords_array = explode(',', $keywords_string);
            foreach ($keywords_array as $keyword) {
                $trimmed_keyword = trim($keyword);
                if (!empty($trimmed_keyword)) {
                    $all_keywords[] = $trimmed_keyword;
                }
            }
        }
    }

    return array_unique($all_keywords);
}

function get_all_page_seo_keywords(): array
{
    $sql = "SELECT slug, seo_keywords FROM pages WHERE is_active = 1 AND seo_keywords IS NOT NULL AND seo_keywords != ''";
    $results = db_query($sql, [], false, true);

    $keyword_data = [];
    if (is_array($results)) {
        foreach ($results as $row) {
            $page_slug = $row['slug'];
            $keywords_string = $row['seo_keywords'];
            $keywords_array = explode(',', $keywords_string);
            foreach ($keywords_array as $keyword) {
                $trimmed_keyword = trim($keyword);
                if (!empty($trimmed_keyword)) {




                    $keyword_data[] = ['keyword' => $trimmed_keyword, 'slug' => $page_slug];
                }
            }
        }
    }




    return $keyword_data;
}
